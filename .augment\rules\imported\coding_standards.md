---
type: "agent_requested"
description: "Example description"
---
# Dota 2 Predictor - Coding Standards for AI Agent

## 🎯 Project-Specific Standards

### File Naming Conventions
- **Model Files**: `xgb_model_[purpose].pkl` (e.g., `xgb_model_hero_pick.pkl`)
- **Scaler Files**: `scaler_[purpose].pkl` (e.g., `scaler_dota_plus.pkl`)
- **Training Scripts**: `create_model_[purpose].py` (e.g., `create_model_match_predict.py`)
- **Test Files**: `test_[module].py` (e.g., `test_ml.py`)

### Import Organization
```python
# Standard library imports
import os
import sys
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

# Third-party imports
import numpy as np
import pandas as pd
import xgboost as xgb
from sqlalchemy import create_engine
import telebot

# Local imports
from config import DATABASE_URL, BOT_TOKEN
from ml.model import MainML
from db.database_operations import get_history_data_as_dataframe
from structure.helpers import prepare_match_prediction_data
```

### Function Documentation Standards
```python
def predict_match_outcome(team1_heroes: List[int], team2_heroes: List[int], 
                         match_duration: Optional[int] = None) -> Dict[str, float]:
    """
    Predict match outcome based on hero compositions and optional match duration.
    
    Args:
        team1_heroes: List of hero IDs for radiant team
        team2_heroes: List of hero IDs for dire team  
        match_duration: Optional match duration in seconds
        
    Returns:
        Dictionary containing prediction probabilities:
        {
            'radiant_win_probability': float,
            'dire_win_probability': float,
            'confidence': float
        }
        
    Raises:
        ValueError: If hero lists are invalid or empty
        ModelNotLoadedError: If prediction model is not loaded
        
    Example:
        >>> predict_match_outcome([1, 2, 3, 4, 5], [6, 7, 8, 9, 10])
        {'radiant_win_probability': 0.65, 'dire_win_probability': 0.35, 'confidence': 0.8}
    """
```

### Error Handling Standards
```python
# Use specific exception types
class ModelNotLoadedError(Exception):
    """Raised when attempting to use an unloaded model"""
    pass

class InvalidHeroError(Exception):
    """Raised when invalid hero ID is provided"""
    pass

# Implement comprehensive error handling
def load_model_safely(model_path: str) -> xgb.Booster:
    """Load XGBoost model with proper error handling"""
    if not Path(model_path).exists():
        raise FileNotFoundError(f"Model file not found: {model_path}")
    
    try:
        model = xgb.Booster()
        model.load_model(model_path)
        logger.info(f"Successfully loaded model: {model_path}")
        return model
    except Exception as e:
        logger.error(f"Failed to load model {model_path}: {e}")
        raise ModelNotLoadedError(f"Could not load model: {e}")
```

### Logging Standards
```python
import logging

# Configure logging at module level
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dota2predictor.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Use structured logging
def log_prediction_request(user_id: int, match_id: Optional[int] = None):
    """Log prediction request with structured data"""
    logger.info(
        "Prediction request",
        extra={
            'user_id': user_id,
            'match_id': match_id,
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': 'prediction_request'
        }
    )
```

### Database Operation Standards
```python
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager

@contextmanager
def database_session():
    """Database session context manager with proper cleanup"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database operation failed: {e}")
        raise
    finally:
        session.close()

# Use parameterized queries
def get_match_predictions(user_id: int, limit: int = 10) -> List[Dict]:
    """Get recent predictions for a user"""
    with database_session() as session:
        results = session.execute(
            text("SELECT * FROM predictions WHERE user_id = :user_id ORDER BY created_at DESC LIMIT :limit"),
            {'user_id': user_id, 'limit': limit}
        ).fetchall()
        return [dict(row) for row in results]
```

### Bot Command Standards
```python
# Use consistent command structure
@bot.message_handler(commands=['predict'])
def handle_predict_command(message):
    """Handle match prediction command"""
    user_id = message.from_user.id
    
    try:
        # Log command usage
        logger.info(f"Predict command used by user {user_id}")
        
        # Validate input
        if not validate_predict_input(message.text):
            bot.reply_to(message, "❌ Invalid format. Use: /predict <match_id>")
            return
        
        # Extract parameters
        match_id = extract_match_id(message.text)
        
        # Process prediction
        result = process_match_prediction(match_id)
        
        # Format response with emojis
        response = format_prediction_response(result)
        
        # Send response
        bot.reply_to(message, response, parse_mode='Markdown')
        
        # Log successful prediction
        log_prediction_success(user_id, match_id, result)
        
    except Exception as e:
        logger.error(f"Predict command failed for user {user_id}: {e}")
        bot.reply_to(message, "⚠️ Prediction failed. Please try again later.")

# Use consistent emoji patterns
EMOJI_PATTERNS = {
    'success': '✅',
    'error': '❌', 
    'warning': '⚠️',
    'info': 'ℹ️',
    'loading': '⏳',
    'radiant': '🌟',
    'dire': '🔥',
    'prediction': '🎯',
    'accuracy': '📊'
}
```

### Model Training Standards
```python
def train_model_with_validation(X_train: np.ndarray, y_train: np.ndarray, 
                               X_val: np.ndarray, y_val: np.ndarray,
                               model_name: str) -> xgb.Booster:
    """Train XGBoost model with proper validation and GPU support"""
    
    # GPU-optimized parameters for RTX 4070
    params = {
        'objective': 'binary:logistic',
        'eval_metric': 'logloss',
        'device': 'cuda',
        'tree_method': 'hist',
        'max_depth': 6,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'random_state': 42
    }
    
    # Create DMatrix objects
    dtrain = xgb.DMatrix(X_train, label=y_train)
    dval = xgb.DMatrix(X_val, label=y_val)
    
    # Train with early stopping
    model = xgb.train(
        params,
        dtrain,
        num_boost_round=1000,
        evals=[(dtrain, 'train'), (dval, 'val')],
        early_stopping_rounds=50,
        verbose_eval=100
    )
    
    # Save model with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = f"xgb_model_{model_name}_{timestamp}.pkl"
    model.save_model(model_path)
    
    # Log training completion
    logger.info(f"Model training completed: {model_path}")
    
    return model
```

### Testing Standards
```python
import pytest
from unittest.mock import Mock, patch

class TestMatchPrediction:
    """Test class for match prediction functionality"""
    
    @pytest.fixture
    def sample_match_data(self):
        """Provide sample match data for testing"""
        return {
            'radiant_team': [1, 2, 3, 4, 5],
            'dire_team': [6, 7, 8, 9, 10],
            'duration': 1800
        }
    
    @pytest.fixture
    def mock_model(self):
        """Mock XGBoost model for testing"""
        model = Mock()
        model.predict.return_value = np.array([0.65])
        return model
    
    def test_predict_match_outcome_success(self, sample_match_data, mock_model):
        """Test successful match outcome prediction"""
        with patch('ml.model.MainML.load_model', return_value=mock_model):
            predictor = MainML()
            result = predictor.predict_match_outcome(**sample_match_data)
            
            assert 'radiant_win_probability' in result
            assert 'dire_win_probability' in result
            assert result['radiant_win_probability'] > 0.5
    
    def test_predict_match_outcome_invalid_heroes(self):
        """Test prediction with invalid hero data"""
        with pytest.raises(ValueError):
            predictor = MainML()
            predictor.predict_match_outcome([], [1, 2, 3, 4, 5])
```

### Configuration Management
```python
# Use environment variables for configuration
import os
from dotenv import load_dotenv

load_dotenv()

# Database configuration
DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://localhost/dota2predictor')
DATABASE_POOL_SIZE = int(os.getenv('DATABASE_POOL_SIZE', '10'))

# Bot configuration  
BOT_TOKEN = os.getenv('BOT_TOKEN')
if not BOT_TOKEN:
    raise ValueError("BOT_TOKEN environment variable is required")

# Model configuration
MODEL_PATH = os.getenv('MODEL_PATH', './models/')
GPU_ENABLED = os.getenv('GPU_ENABLED', 'true').lower() == 'true'

# API configuration
OPENDOTA_API_BASE = 'https://api.opendota.com/api'
API_RATE_LIMIT = int(os.getenv('API_RATE_LIMIT', '1'))  # requests per second
```

These coding standards ensure consistency, maintainability, and reliability across the Dota 2 Predictor codebase while leveraging the project's specific architecture and requirements.
