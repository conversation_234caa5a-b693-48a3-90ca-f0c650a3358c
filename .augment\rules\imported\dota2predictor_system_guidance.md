---
type: "agent_requested"
description: "Example description"
---
# Dota 2 Match Result Predictor - AI Coding Agent System Guidance

## 🎯 Project Overview

This is a **Dota 2 Match Result Predictor Telegram Bot** that uses machine learning to predict match outcomes in real-time. The system combines professional match data analysis with user-friendly Telegram interface to provide accurate predictions and insights.

### Core Purpose
- Predict Dota 2 match outcomes using XGBoost ML models
- Provide real-time analysis through Telegram bot interface
- Track prediction accuracy and model performance
- Support multiple prediction modes for comprehensive analysis

## 🏗️ Architecture Context

### Three Core ML Models
1. **Match Prediction** (`xgb_model.pkl`) - Primary win/loss prediction
2. **Hero Pick Analysis** (`xgb_model_hero_pick.pkl`) - Team composition strength analysis
3. **Dota Plus** (`xgb_model_dota_plus.pkl`) - Live match win probability tracking

### Data Sources
- **OpenDota API**: Historical professional match data for training
- **Steam API**: Live match data for real-time predictions
- **PostgreSQL Database**: Prediction storage, user tracking, model performance metrics

### Technology Stack
- **ML Framework**: XGBoost with GPU acceleration (CUDA 12.4, RTX 4070)
- **Backend**: Python 3.11 with SQLAlchemy ORM
- **Database**: PostgreSQL for persistent storage
- **Bot Interface**: pyTelegramBotAPI for user interaction
- **Deployment**: Docker Compose for containerized deployment
- **Environment**: Mamba/Miniforge for dependency management

## 🛠️ Development Environment

### Mamba Environment Setup
- **Environment Name**: `dota2predictor-env`
- **Python Version**: 3.11 (optimal for ML libraries)
- **GPU Support**: CUDA 12.4 toolkit for RTX 4070 acceleration
- **Key Dependencies**: XGBoost (GPU-enabled), NumPy, Pandas, Scikit-learn

### Environment Commands
```bash
mamba activate dota2predictor-env  # Always start with this
python verify_gpu.py              # Test GPU functionality
jupyter lab                       # ML experimentation
python start.py                  # Run the bot
```

## 📁 Code Organization

### Directory Structure
```
├── ml/                    # ML model training and management
│   ├── model.py          # Main ML class and prediction logic
│   ├── create_model_*.py # Model training scripts
├── db/                   # Database operations and setup
│   ├── database_operations.py # Core DB functions
│   ├── setup.py         # Database initialization
├── structure/            # Data processing and API integrations
│   ├── helpers.py       # Data preparation utilities
│   ├── opendota.py      # OpenDota API integration
│   ├── struct.py        # Data structures and validation
├── dataset/              # Training data management
├── tests/                # Unit tests for all components
├── *.pkl                # Trained model files
├── start.py             # Bot entry point
└── config.py            # Configuration management
```

### Key Files
- **Model Files**: `xgb_model*.pkl` - Serialized XGBoost models
- **Scalers**: `scaler*.pkl` - Feature scaling objects
- **Bot Entry**: `start.py` - Main application entry point
- **Environment**: `environment.yml` - Mamba environment specification

## 📋 Best Practices

### ML Model Handling
- **GPU Utilization**: Always use `device='cuda'` for XGBoost training on RTX 4070
- **Model Serialization**: Use pickle for model persistence, include version tracking
- **Feature Scaling**: Maintain separate scalers for each model type
- **Validation**: Implement cross-validation and performance monitoring
- **Incremental Learning**: Support model updates without full retraining

### Database Operations
- **ORM Usage**: Use SQLAlchemy for all database interactions
- **Connection Management**: Implement proper connection pooling and cleanup
- **Schema Migrations**: Version control database schema changes
- **Performance**: Index frequently queried columns, optimize query patterns
- **Data Integrity**: Validate data before insertion, handle duplicates gracefully

### API Integrations
- **Rate Limiting**: Respect OpenDota and Steam API rate limits
- **Error Handling**: Implement robust retry mechanisms with exponential backoff
- **Data Validation**: Validate API responses before processing
- **Caching**: Cache frequently accessed data to reduce API calls
- **Monitoring**: Log API usage and response times

### Bot Command Processing
- **Command Structure**: Use clear, intuitive command patterns
- **Error Messages**: Provide helpful error messages to users
- **State Management**: Handle user sessions and conversation state
- **Performance**: Respond quickly, use async operations where possible
- **Logging**: Log all user interactions for debugging and analytics

## 🔄 Common Development Tasks

### Model Retraining Workflow
1. **Data Collection**: Fetch latest match data from OpenDota API
2. **Data Preprocessing**: Clean, validate, and feature engineer data
3. **Model Training**: Use GPU-accelerated XGBoost with cross-validation
4. **Model Evaluation**: Compare performance against previous version
5. **Model Deployment**: Replace existing .pkl files with new models
6. **Performance Monitoring**: Track prediction accuracy in production

### Adding New Prediction Features
1. **Feature Engineering**: Identify and implement new data features
2. **Model Updates**: Retrain models with new feature set
3. **API Updates**: Modify prediction endpoints to handle new features
4. **Bot Commands**: Add new Telegram commands for feature access
5. **Testing**: Comprehensive testing of new functionality
6. **Documentation**: Update user guides and technical documentation

### Database Schema Updates
1. **Migration Scripts**: Create versioned migration scripts
2. **Backward Compatibility**: Ensure existing data remains accessible
3. **Index Updates**: Add/modify indexes for new query patterns
4. **Data Validation**: Validate existing data against new schema
5. **Testing**: Test migrations on development database first

### Bot Command Enhancements
1. **Command Design**: Follow existing command patterns and naming
2. **Input Validation**: Validate user inputs and provide clear feedback
3. **Response Formatting**: Use consistent message formatting and emojis
4. **Error Handling**: Graceful error handling with user-friendly messages
5. **Help System**: Update help commands with new functionality

## 📦 Key Dependencies

### Core ML Libraries
- **XGBoost**: Gradient boosting framework with GPU support
- **NumPy**: Numerical computing foundation
- **Pandas**: Data manipulation and analysis
- **Scikit-learn**: ML utilities and preprocessing
- **Joblib**: Model serialization and parallel processing

### Database and APIs
- **SQLAlchemy**: Python SQL toolkit and ORM
- **psycopg2**: PostgreSQL adapter for Python
- **requests**: HTTP library for API calls
- **beautifulsoup4**: HTML/XML parsing for web scraping

### Bot and Utilities
- **pyTelegramBotAPI**: Telegram Bot API wrapper
- **python-dotenv**: Environment variable management
- **tqdm**: Progress bars for long-running operations
- **pandera**: Data validation framework

### Development Tools
- **pytest**: Testing framework
- **pre-commit**: Code quality hooks
- **coverage**: Test coverage measurement
- **jupyterlab**: Interactive development environment

## 🎯 Coding Standards

### Code Quality
- **Type Hints**: Use type hints for function parameters and returns
- **Docstrings**: Document all functions, classes, and modules
- **Error Handling**: Implement comprehensive exception handling
- **Logging**: Use structured logging throughout the application
- **Testing**: Maintain high test coverage for all components

### Performance Considerations
- **GPU Utilization**: Leverage GPU acceleration for ML training
- **Memory Management**: Monitor memory usage during large data processing
- **Async Operations**: Use async/await for I/O bound operations
- **Caching**: Implement intelligent caching strategies
- **Database Optimization**: Use efficient queries and proper indexing

### Security and Reliability
- **Input Validation**: Validate all external inputs
- **API Security**: Secure API keys and tokens
- **Error Recovery**: Implement graceful degradation and recovery
- **Monitoring**: Log errors and performance metrics
- **Backup Strategy**: Regular database backups and model versioning

## 🔧 Implementation Patterns

### Model Loading Pattern
```python
# Standard model loading with error handling
def load_model(model_path: str) -> xgb.Booster:
    try:
        model = xgb.Booster()
        model.load_model(model_path)
        return model
    except Exception as e:
        logger.error(f"Failed to load model {model_path}: {e}")
        raise
```

### Database Query Pattern
```python
# Use context managers for database operations
def get_match_data(match_id: int) -> Optional[Dict]:
    with get_db_session() as session:
        try:
            result = session.query(Match).filter_by(id=match_id).first()
            return result.to_dict() if result else None
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            session.rollback()
            raise
```

### API Integration Pattern
```python
# Implement retry logic with exponential backoff
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def fetch_opendota_data(endpoint: str) -> Dict:
    response = requests.get(f"{OPENDOTA_BASE_URL}/{endpoint}")
    response.raise_for_status()
    return response.json()
```

### Bot Command Pattern
```python
# Standard bot command structure
@bot.message_handler(commands=['predict'])
def handle_predict_command(message):
    try:
        # Input validation
        if not validate_input(message.text):
            bot.reply_to(message, "❌ Invalid input format")
            return

        # Process request
        result = process_prediction(message.text)

        # Format response
        response = format_prediction_response(result)
        bot.reply_to(message, response)

    except Exception as e:
        logger.error(f"Command processing failed: {e}")
        bot.reply_to(message, "⚠️ An error occurred. Please try again.")
```

## 📊 Performance Guidelines

### XGBoost GPU Training
- **Batch Size**: Use 1000-5000 samples for RTX 4070 (12GB VRAM)
- **Tree Method**: Always use `tree_method='hist'` for GPU acceleration
- **Memory Management**: Monitor VRAM usage with `nvidia-smi`
- **Parameters**: Optimize `max_depth`, `learning_rate`, and `n_estimators` for GPU

### Database Performance
- **Connection Pooling**: Use SQLAlchemy connection pooling
- **Query Optimization**: Use EXPLAIN ANALYZE for slow queries
- **Indexing Strategy**: Index foreign keys and frequently filtered columns
- **Batch Operations**: Use bulk operations for large data inserts

This guidance serves as the foundational context for all development work on the Dota 2 Match Result Predictor project, ensuring consistency, quality, and maintainability across all code changes and enhancements.
