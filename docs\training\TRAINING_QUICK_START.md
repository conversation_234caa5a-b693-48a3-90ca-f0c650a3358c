# Dota 2 Predictor - Training Quick Start

This document provides a concise reference for the main model training commands. For detailed explanations, troubleshooting, and advanced configuration, please refer to the full [MODEL_TRAINING_GUIDE.md](./MODEL_TRAINING_GUIDE.md).

---

## Method 1: Kaggle Dataset (Recommended)

**Advantages**: No API limits, faster, more reliable, enhanced features, GPU acceleration. This is the preferred method for all training.

```bash
# Activate Mamba environment with GPU support
mamba activate dota2predictor

# Verify GPU functionality
python verify_gpu.py

# Step 1: Validate data quality
python dataset/validate_training_data.py

# Step 2: Process Kaggle dataset with player-hero win rates
python dataset/create_training_data_from_kaggle.py

# Step 3: Train model with GPU acceleration and full 370-feature set
python ml/create_model_match_predict.py
```

---

## Method 2: Incremental Learning (Automatic)

**How it works**: The system automatically retrains the model with new data as predictions are made and results are collected. This process is handled by the bot and requires no manual intervention.

**Configuration** in `config.py`:
```python
incremental_learning_batch = 50  # Retrain after 50 new results
```

---

## Method 3: Full Manual Retraining (Legacy)

**Use case**: Complete model overhaul with new datasets. This method is deprecated and should only be used if the Kaggle dataset method is not feasible.

```bash
# Activate Mamba environment
mamba activate dota2predictor-env

# Step 1: Generate new training data (takes hours and makes many API calls)
python dataset/generate_dataset_match_predict.py

# Step 2: Manually combine tournament CSV files
# Merge all premium_league_matches_*.csv into all_data_match_predict.csv

# Step 3: Train new model with GPU acceleration
python ml/create_model_match_predict.py
